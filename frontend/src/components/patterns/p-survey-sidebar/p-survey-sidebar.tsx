import { Component, h, Prop } from '@stencil/core';

@Component({
  tag: 'p-survey-sidebar',
  styleUrl: 'p-survey-sidebar.css',
  shadow: true,
})
export class PSurveySidebar {
  @Prop() surveyId: string;
  @Prop() activePage: 'overview' | 'analytics' | 'responses' | 'export';

  render() {
    return (
      <div id="sidebar">
        <e-link url="/">← Back</e-link>
        <l-spacer value={2}></l-spacer>
        <div class="sidebar-nav">
          <div class={`nav-item ${this.activePage === 'overview' ? 'nav-item--active' : ''}`}>
            {this.activePage === 'overview' ? (
              <e-text>Overview</e-text>
            ) : (
              <e-link url={`/surveys/${this.surveyId}/overview`}>
                <e-text>Overview</e-text>
              </e-link>
            )}
          </div>
          <div class={`nav-item ${this.activePage === 'analytics' ? 'nav-item--active' : ''}`}>
            {this.activePage === 'analytics' ? (
              <e-text>Analytics</e-text>
            ) : (
              <e-link url={`/surveys/${this.surveyId}/analytics`}>
                <e-text>Analytics</e-text>
              </e-link>
            )}
          </div>
          <div class={`nav-item ${this.activePage === 'responses' ? 'nav-item--active' : ''}`}>
            {this.activePage === 'responses' ? (
              <e-text>Responses</e-text>
            ) : (
              <e-link url={`/surveys/${this.surveyId}/responses`}>
                <e-text>Responses</e-text>
              </e-link>
            )}
          </div>
          <div class={`nav-item ${this.activePage === 'export' ? 'nav-item--active' : ''}`}>
            {this.activePage === 'export' ? (
              <e-text>Export</e-text>
            ) : (
              <e-link url={`/surveys/${this.surveyId}/export`}>
                <e-text>Export</e-text>
              </e-link>
            )}
          </div>
        </div>
      </div>
    );
  }
}
