import { Component, FunctionalComponent, Host, Listen, State, h } from '@stencil/core';
import { Router } from '../../';
import { Route, match } from 'stencil-router-v2';
import { Store } from '../../global/script/store';
import {
  setAccountDataInStore,
  generateEmailVerificationPayload,
  validateEmailVerificationPayload,
  emailVerificationApi,
} from './helpers';
import { MailApi } from '../../global/script/helpers';
import { MailPayloadInterface } from '../../global/script/interfaces';
import { GenerateMailPayload, ValidateMailPayload } from '../../global/script/helpers';
import {
  AccountDetailsBySessionApi,
  CheckSessionStatusApi,
  GetCsrfTokenApi,
  LogoutApi,
} from '../../global/script/helpers';
import { emailVerificationPayloadInterface } from './interfaces';
import { FrontendLogger } from '../../global/script/var';

@Component({
  tag: 'app-root',
  styleUrl: 'app-root.css',
  shadow: true,
})
export class AppRoot {
  @State() isMailingEmailVerificationCode: boolean = false;
  @State() isSessionChecked: boolean = false;
  @State() isVerifyingEmail: boolean = false;

  private emailVerificationCode: string = '';

  @Listen('authSuccessfulEvent') async handleAuthSuccessfulEvent() {
    // DEBUG: Log authentication success event
    FrontendLogger.debug('Authentication successful event received', {
      currentSessionActive: Store.isSessionActive,
      hasToken: !!Store.csrfToken,
    });

    await new Promise(resolve => setTimeout(resolve, 500));
    this.initSession();
  }

  @Listen('buttonClickEvent') async handleButtonClickEvent(e) {
    // DEBUG: Log button click events
    FrontendLogger.debug('Button click event received', {
      action: e.detail.action,
      value: e.detail.value,
      hasDetail: !!e.detail,
    });

    if (e.detail.action === 'mailEmailVerificationCode') {
      this.mailEmailVerificationCode();
    } else if (e.detail.action === 'logout') {
      this.logout();
    } else if (e.detail.action === 'proceedToLogin') {
      FrontendLogger.debug('Navigating to login page');
      Router.push('/login');
    } else if (e.detail.action === 'routeTo') {
      FrontendLogger.debug('Navigating to route', { route: e.detail.value });
      Router.push(e.detail.value);
    }
  }

  @Listen('inputEvent') handleInputEvent(e) {
    // DEBUG: Log input events
    FrontendLogger.debug('Input event received', {
      name: e.detail.name,
      valueLength: e.detail.value?.length || 0,
      hasValue: !!e.detail.value,
    });

    if (e.detail.name === 'emailVerificationCode') {
      this.emailVerificationCode = e.detail.value;
      FrontendLogger.debug('Email verification code updated', {
        codeLength: this.emailVerificationCode.length,
        isComplete: this.emailVerificationCode.length === 6,
      });

      if (this.emailVerificationCode.length === 6) {
        FrontendLogger.debug('Email verification code complete, triggering verification');
        this.verifyEmail();
      }
    }
  }

  @Listen('logoutEvent') handleLogout() {
    FrontendLogger.debug('Logout event received');
    this.logout();
  }

  @Listen('routeToEvent') handleRouteToEvent(e) {
    FrontendLogger.debug('Route to event received', { route: e.detail.route });
    Router.push(e.detail.route);
  }

  @Listen('authSuccess') handleAuthSuccessEvent(e) {
    FrontendLogger.debug('Auth success event received', {
      hasPayload: !!e.detail.payload,
      payloadKeys: e.detail.payload ? Object.keys(e.detail.payload) : [],
    });
    setAccountDataInStore(e.detail.payload);
  }

  componentDidLoad() {
    // DEBUG: Log component initialization
    FrontendLogger.debug('App root component loaded, initializing session', {
      currentPath: window.location.pathname,
      hasToken: !!Store.csrfToken,
      isSessionActive: Store.isSessionActive,
    });
    this.initSession();
  }

  async initSession() {
    // DEBUG: Log session initialization start
    FrontendLogger.debug('Session initialization started', {
      hasExistingToken: !!Store.csrfToken,
      currentSessionActive: Store.isSessionActive,
      isSessionChecked: this.isSessionChecked,
    });

    this.isSessionChecked = false;

    try {
      // Ensure we have a CSRF token
      if (!Store.csrfToken) {
        FrontendLogger.debug('No CSRF token found, fetching new token');
        const csrfResult = await GetCsrfTokenApi();
        if (!csrfResult.success) {
          FrontendLogger.error('Failed to fetch CSRF token during session init', {
            error: csrfResult.message,
          });
        } else {
          FrontendLogger.debug('CSRF token fetched successfully during session init');
        }
      } else {
        FrontendLogger.debug('Using existing CSRF token for session init');
      }

      // Check session status
      FrontendLogger.debug('Checking session status');
      const sessionResult = await CheckSessionStatusApi();

      FrontendLogger.debug('Session status check completed', {
        success: sessionResult.success,
        hasPayload: !!sessionResult.payload,
        isSessionActive: sessionResult.payload?.isSessionActive,
      });

      if (sessionResult.success && sessionResult.payload && sessionResult.payload.isSessionActive) {
        Store.isSessionActive = true;
        FrontendLogger.debug('Session is active, updating store');

        // Fetch account details if we have a valid CSRF token
        if (Store.csrfToken) {
          FrontendLogger.debug('Fetching account details for active session');
          this.fetchAccountDetails();
        } else {
          FrontendLogger.warn('Session active but no CSRF token available');
        }
      } else {
        FrontendLogger.debug('Session is not active, clearing store data');
        Store.isSessionActive = false;
        Store.accountName = '';
        Store.accountEmail = '';
      }
    } catch (error) {
      FrontendLogger.error('Error during session initialization', {
        error: error instanceof Error ? error.message : String(error),
        stack: error instanceof Error ? error.stack?.split('\n').slice(0, 3).join('\n') : undefined,
      });

      // Reset session state on error
      Store.isSessionActive = false;
      Store.csrfToken = '';
    } finally {
      this.isSessionChecked = true;
      FrontendLogger.debug('Session initialization completed', {
        isSessionActive: Store.isSessionActive,
        hasToken: !!Store.csrfToken,
        accountName: Store.accountName,
        isSessionChecked: this.isSessionChecked,
      });
    }
  }

  /**
   * Fetches account details for the current session
   *
   * This method retrieves user account information and updates the global store.
   * It's called after successful session validation.
   */
  async fetchAccountDetails() {
    try {
      // DEBUG: Log account details fetch attempt
      FrontendLogger.debug('Fetching account details for active session');

      const { success, payload } = await AccountDetailsBySessionApi();
      if (success && payload) {
        FrontendLogger.debug('Account details fetched successfully', {
          hasPayload: !!payload,
          payloadKeys: payload ? Object.keys(payload) : [],
        });
        setAccountDataInStore(payload);
      } else {
        FrontendLogger.warn('Account details fetch failed or returned no data', {
          success,
          hasPayload: !!payload,
        });
      }
    } catch (error) {
      // DEBUG: Log account details fetch error
      FrontendLogger.error('Failed to fetch account details', {
        error: error instanceof Error ? error.message : String(error),
        stack: error instanceof Error ? error.stack?.split('\n').slice(0, 3).join('\n') : undefined,
      });
    }
  }

  async logout() {
    // DEBUG: Log logout initiation
    FrontendLogger.debug('Logout process initiated');

    let { success, message } = await LogoutApi();

    // Clear session data regardless of API result
    Store.isSessionActive = false;
    Store.accountName = '';
    Store.accountEmail = '';
    Store.isEmailVerified = true;

    if (!success) {
      FrontendLogger.warn('Logout API failed, but continuing with logout flow', { message });
      if (!message?.includes('Invalid security token')) {
        alert(message);
      }
    }

    // Fetching anonymous CSRF token after logout
    try {
      FrontendLogger.debug('Fetching new CSRF token after logout');
      await GetCsrfTokenApi();
    } catch (error) {
      FrontendLogger.warn('Failed to fetch new CSRF token after logout', {
        error: error instanceof Error ? error.message : String(error),
      });
    }

    FrontendLogger.debug('Logout complete, navigating to home');
    Router.push('/');
  }

  async mailEmailVerificationCode() {
    let mailEmailVerificationCodePayload: MailPayloadInterface = GenerateMailPayload(
      Store.accountEmail,
      'emailVerificationCode',
    );
    let { isValid, validationMessage } = ValidateMailPayload(mailEmailVerificationCodePayload);
    if (!isValid) {
      return alert(validationMessage);
    }
    this.isMailingEmailVerificationCode = true;
    let { message } = await MailApi(mailEmailVerificationCodePayload);
    this.isMailingEmailVerificationCode = false;
    alert(message);
  }

  async verifyEmail() {
    let emailVerificationPayload: emailVerificationPayloadInterface =
      generateEmailVerificationPayload(Store.accountEmail, this.emailVerificationCode);
    let { isValid, validationMessage } = validateEmailVerificationPayload(emailVerificationPayload);
    if (!isValid) {
      return alert(validationMessage);
    }
    this.isVerifyingEmail = true;
    let { success, message } = await emailVerificationApi(emailVerificationPayload);
    alert(message);
    if (!success) {
      return;
    }
    Store.isEmailVerified = true;
  }

  EmailVerificationBanner: FunctionalComponent = () => (
    <c-banner position="bottom" theme="danger">
      <div class="hide-on-mobile">
        <l-row justifyContent="space-between">
          <l-row>
            <e-text>
              We have sent an email verification code to{' '}
              <u>
                <strong>{Store.accountEmail}</strong>
              </u>
            </e-text>
            <l-spacer variant="horizontal" value={0.25}></l-spacer>
            <e-input
              type="text"
              name="emailVerificationCode"
              placeholder="Enter verification code"
            ></e-input>
          </l-row>
          {this.isMailingEmailVerificationCode ? (
            <e-spinner theme="blue"></e-spinner>
          ) : (
            <e-button variant="link" action="mailEmailVerificationCode">
              Re-send code
            </e-button>
          )}
        </l-row>
      </div>
      <div class="show-on-mobile">
        <e-text>
          We have sent an email verification code to{' '}
          <strong>
            <u>{Store.accountEmail}</u>
          </strong>
        </e-text>
        <l-spacer value={1}></l-spacer>
        <l-row>
          <e-input
            type="text"
            name="emailVerificationCode"
            placeholder="Enter verification code"
          ></e-input>
          {this.isMailingEmailVerificationCode ? (
            <e-spinner theme="blue"></e-spinner>
          ) : (
            <e-button variant="link" action="mailEmailVerificationCode">
              Re-send code
            </e-button>
          )}
        </l-row>
      </div>
    </c-banner>
  );

  private getRedirectRoutes() {
    if (Store.isSessionActive) {
      return [
        <Route path={/^\/$/} to="/home" />,
        <Route path={/^\/login$/} to="/home" />,
        <Route path={/^\/signup$/} to="/home" />,
        <Route path={/^\/auth\/callback\/[^\/]+$/} to="/home" />,
        <Route path={/^\/password-reset$/} to="/home" />,
      ];
    } else {
      return [
        <Route path={/^\/$/} to="/login" />,
        <Route path={/^\/home$/} to="/login" />,
        <Route path={/^\/billing/} to="/login" />,
        <Route path={/^\/support/} to="/login" />,
        <Route path={/^\/account/} to="/login" />,
        <Route path={/^\/account\/delete/} to="/login" />,
        <Route path={/^\/checkout$/} to="/login" />,
        <Route path={/^\/payment\/[^\/]+\/success$/} to="/login" />,
        <Route path={/^\/payment\/[^\/]+\/failed$/} to="/login" />,
        <Route path={/^\/surveys\/create$/} to="/login" />,
        <Route path={/^\/surveys\/[^\/]+\/delete$/} to="/login" />,
        <Route path={/^\/surveys\/[^\/]+\/edit$/} to="/login" />,
        <Route path={/^\/surveys\/[^\/]+\/embed$/} to="/login" />,
        <Route path={/^\/surveys\/[^\/]+\/share$/} to="/login" />,
        <Route path={/^\/surveys\/[^\/]+\/overview$/} to="/login" />,
        <Route path={/^\/surveys\/[^\/]+\/analytics$/} to="/login" />,
        <Route path={/^\/surveys\/[^\/]+\/responses$/} to="/login" />,
        <Route path={/^\/surveys\/[^\/]+\/export$/} to="/login" />,
        <Route path={/^\/s$/} to="/login" />,
      ];
    }
  }

  render() {
    if (!this.isSessionChecked) {
      return (
        <Host>
          <div id="init-loader">
            <e-spinner theme="dark"></e-spinner>
          </div>
        </Host>
      );
    }

    return (
      <Host>
        {Store.isSessionActive && <p-topbar></p-topbar>}

        <Router.Switch>
          {this.getRedirectRoutes()}
          {/* Auth Routes */}
          <Route path="/login" render={() => <v-login />}></Route>
          <Route path="/signup" render={() => <v-signup />}></Route>
          <Route
            path={match('/auth/callback/:provider')}
            render={({ provider }) => <v-post-oauth provider={provider} />}
          />
          <Route path="/password-reset" render={() => <v-password-reset />}></Route>
          <Route path="/home" render={() => <v-home />}></Route>
          <Route path="/billing" render={() => <v-billing />}></Route>
          <Route path="/support" render={() => <v-support />}></Route>
          <Route path="/account" render={() => <v-account />}></Route>
          <Route path="/account/delete" render={() => <v-delete-account />}></Route>
          <Route
            path="/checkout/:orderId"
            render={({ orderId }) => <v-checkout orderId={orderId} />}
          />
          <Route
            path={match('/payment/:sessionId/success')}
            render={({ sessionId }) => <v-payment-success sessionId={sessionId} />}
          />
          <Route
            path={match('/payment/:sessionId/failed')}
            render={({ sessionId }) => <v-payment-failed sessionId={sessionId} />}
          />
          {/* Survey Routes */}
          <Route path="/surveys/create" render={() => <v-create-survey />}></Route>
          <Route
            path={match('/surveys/:surveyId/delete')}
            render={({ surveyId }) => <v-delete-survey surveyId={surveyId} />}
          />
          <Route
            path={match('/surveys/:surveyId/edit')}
            render={({ surveyId }) => <v-edit-survey surveyId={surveyId} />}
          />
          <Route
            path={match('/surveys/:surveyId/embed')}
            render={({ surveyId }) => <v-embed-survey surveyId={surveyId} />}
          />
          <Route
            path={match('/surveys/:surveyId/share')}
            render={({ surveyId }) => <v-share-survey surveyId={surveyId} />}
          />
          <Route
            path={match('/surveys/:surveyId/overview')}
            render={({ surveyId }) => <v-survey-overview surveyId={surveyId} />}
          />
          <Route
            path={match('/surveys/:surveyId/analytics')}
            render={({ surveyId }) => <v-survey-analytics surveyId={surveyId} />}
          />
          <Route
            path={match('/surveys/:surveyId/responses')}
            render={({ surveyId }) => <v-survey-responses surveyId={surveyId} />}
          />
          <Route
            path={match('/surveys/:surveyId/export')}
            render={({ surveyId }) => <v-survey-export surveyId={surveyId} />}
          />
          <Route
            path={match('/s/:shareKey')}
            render={({ shareKey }) => <v-shared-survey shareKey={shareKey} />}
          />
          <Route path={/.*/} render={() => <v-catch-all></v-catch-all>}></Route>
        </Router.Switch>

        {Store.isSessionActive && !Store.isEmailVerified && (
          <this.EmailVerificationBanner></this.EmailVerificationBanner>
        )}
      </Host>
    );
  }
}
